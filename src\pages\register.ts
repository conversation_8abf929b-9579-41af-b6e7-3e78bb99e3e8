import { renderHTMLTemplate } from '../utils/template';

export function getRegisterPage(): string {
  const content = `
    <div class="bg-gray-50 min-h-screen flex items-center justify-center p-4">
      <div id="form-container" class="form-container">
        
        <!-- 初始注册页面 -->
        <div id="registerInitial" class="text-center">
          <h2 class="text-2xl font-semibold text-gray-800 mb-10">创建帐户</h2>
          <form class="space-y-6">
            <div class="input-container">
              <input 
                id="registerEmail"
                type="email" 
                placeholder="电子邮件地址" 
                class="w-full px-4 py-4 rounded-full text-center text-gray-600 focus:outline-none transition-all duration-200"
                required
              >
              <label class="input-label">电子邮件地址</label>
            </div>
            <button type="button" id="registerEmailBtn" class="w-full bg-gray-900 text-white py-4 rounded-full font-medium hover:bg-gray-800 transition-colors">
              继续
            </button>
          </form>
          <p class="mt-8 text-gray-600">
            已有帐户？ 
            <a href="/login" class="text-blue-600 hover:underline">请登录</a>
          </p>
        </div>

        <!-- 注册密码设置页面 -->
        <div id="registerPassword" class="text-center hidden">
          <h2 class="text-2xl font-semibold text-gray-800 mb-10">创建帐户</h2>
          <form class="space-y-6">
            <div class="relative">
              <div class="flex items-center justify-between text-sm text-gray-500 mb-2">
                <span>电子邮件地址</span>
              </div>
              <div class="flex items-center border border-gray-300 rounded-full px-4 py-3 bg-gray-50">
                <span id="displayRegisterEmail" class="flex-1 text-gray-700"></span>
                <button type="button" id="editRegisterEmailBtn" class="text-blue-600 hover:underline text-sm font-medium">编辑</button>
              </div>
            </div>
            <div>
              <div class="flex items-center justify-between text-sm text-gray-500 mb-2">
                <span>密码</span>
              </div>
              <div class="relative input-container">
                <input 
                  id="registerPasswordInput"
                  type="password" 
                  class="w-full px-4 py-4 rounded-full text-gray-600 focus:outline-none transition-all duration-200"
                  required
                >
                <label class="input-label">密码</label>
              </div>
            </div>
            <button type="button" id="registerSubmitBtn" class="w-full bg-gray-900 text-white py-4 rounded-full font-medium hover:bg-gray-800 transition-colors">
              继续
            </button>
          </form>
          <p class="mt-8 text-gray-600">
            已有帐户？ 
            <a href="/login" class="text-blue-600 hover:underline">请登录</a>
          </p>
        </div>
      </div>
    </div>

    <script>
      console.log('Register page script loading...');
      
      // 全局变量
      let motion = null;
      
      // 消息提示函数
      function showMessage(message, type = 'info', duration = 3000) {
        const existingMessage = document.getElementById('message-toast');
        if (existingMessage) {
          existingMessage.remove();
        }

        const messageDiv = document.createElement('div');
        messageDiv.id = 'message-toast';
        messageDiv.className = \`message-toast message-\${type}\`;
        
        const icons = {
          success: '✓',
          error: '✕',
          warning: '⚠',
          info: 'ℹ'
        };
        
        messageDiv.innerHTML = \`
          <div class="message-content">
            <span class="message-icon">\${icons[type] || icons.info}</span>
            <span class="message-text">\${message}</span>
            <button class="message-close" onclick="this.parentElement.parentElement.remove()">×</button>
          </div>
        \`;

        document.body.appendChild(messageDiv);

        setTimeout(() => {
          messageDiv.classList.add('message-show');
        }, 10);

        if (duration > 0) {
          setTimeout(() => {
            messageDiv.classList.add('message-hide');
            setTimeout(() => {
              if (messageDiv.parentElement) {
                messageDiv.remove();
              }
            }, 300);
          }, duration);
        }

        return messageDiv;
      }
      
      // 基本函数
      function hideAllForms() {
        const forms = ['registerInitial', 'registerPassword'];
        forms.forEach(id => {
          const element = document.getElementById(id);
          if (element) {
            element.classList.add('hidden');
          }
        });
      }
      
      function showForm(formId) {
        const form = document.getElementById(formId);
        if (form) {
          form.classList.remove('hidden');
          
          // 如果有motion，添加动画
          if (motion) {
            motion(form, {
              initial: { opacity: 0, y: 20 },
              animate: { opacity: 1, y: 0 },
              transition: { duration: 0.3 }
            });
          }
        }
      }
      
      function handleRegisterEmail() {
        console.log('Handling register email');
        const email = document.getElementById('registerEmail').value;
        if (!email) {
          showMessage('请输入邮箱地址', 'warning');
          return;
        }

        // 简单的邮箱格式验证
        const emailRegex = /^[^\\s@]+@[^\\s@]+\\.[^\\s@]+$/;
        if (!emailRegex.test(email)) {
          showMessage('邮箱格式不正确', 'error');
          return;
        }

        document.getElementById('displayRegisterEmail').textContent = email;
        hideAllForms();
        setTimeout(() => showForm('registerPassword'), 100);
      }
      
      function editRegisterEmail() {
        hideAllForms();
        setTimeout(() => showForm('registerInitial'), 100);
      }
      
      function handleRegisterSubmit() {
        console.log('Handling register submit');
        const email = document.getElementById('displayRegisterEmail').textContent;
        const password = document.getElementById('registerPasswordInput').value;
        
        if (!password) {
          showMessage('请输入密码', 'warning');
          return;
        }

        if (password.length < 6) {
          showMessage('密码长度至少6位', 'warning');
          return;
        }

        // 显示加载状态
        const submitBtn = document.getElementById('registerSubmitBtn');
        const originalText = submitBtn.textContent;
        submitBtn.textContent = '注册中...';
        submitBtn.disabled = true;

        const loadingMessage = showMessage('<span class="loading-spinner"></span> 正在注册...', 'info', 0);

        // 调用注册API
        fetch('/api/register', {
          method: 'POST',
          headers: {
            'Content-Type': 'application/json',
          },
          body: JSON.stringify({ email, password })
        })
        .then(response => response.json())
        .then(data => {
          // 移除加载消息
          if (loadingMessage && loadingMessage.parentElement) {
            loadingMessage.remove();
          }

          if (data.success) {
            showMessage('注册成功！即将跳转到登录页面', 'success', 2000);
            setTimeout(() => {
              window.location.href = '/login';
            }, 2000);
          } else {
            showMessage(data.message || '注册失败，请稍后重试', 'error');
          }
        })
        .catch(error => {
          console.error('注册错误:', error);
          // 移除加载消息
          if (loadingMessage && loadingMessage.parentElement) {
            loadingMessage.remove();
          }
          showMessage('网络错误，请稍后重试', 'error');
        })
        .finally(() => {
          // 恢复按钮状态
          submitBtn.textContent = originalText;
          submitBtn.disabled = false;
        });
      }
      
      // DOM加载完成后初始化
      document.addEventListener('DOMContentLoaded', function() {
        console.log('DOM loaded, binding events...');
        
        // 绑定所有按钮事件
        const registerEmailBtn = document.getElementById('registerEmailBtn');
        if (registerEmailBtn) {
          registerEmailBtn.addEventListener('click', function(e) {
            e.preventDefault();
            console.log('Register email button clicked');
            handleRegisterEmail();
          });
        }
        
        const editRegisterEmailBtn = document.getElementById('editRegisterEmailBtn');
        if (editRegisterEmailBtn) {
          editRegisterEmailBtn.addEventListener('click', function(e) {
            e.preventDefault();
            editRegisterEmail();
          });
        }
        
        const registerSubmitBtn = document.getElementById('registerSubmitBtn');
        if (registerSubmitBtn) {
          registerSubmitBtn.addEventListener('click', function(e) {
            e.preventDefault();
            handleRegisterSubmit();
          });
        }
        
        // 初始化显示注册表单
        showForm('registerInitial');
        
        console.log('All events bound successfully');
      });
    </script>
    
    <script type="module">
      import { motion as framerMotion } from 'https://esm.sh/framer-motion@11.0.24';
      
      // 设置全局motion变量
      motion = framerMotion;
      
      document.addEventListener('DOMContentLoaded', function() {
        console.log('Framer Motion loaded');
        
        // 初始化动画
        const formContainer = document.getElementById('form-container');
        if (formContainer) {
          framerMotion(formContainer, {
            initial: { opacity: 0, scale: 0.95 },
            animate: { opacity: 1, scale: 1 },
            transition: { duration: 0.5 }
          });
        }
      });
    </script>
  `;
  
  return renderHTMLTemplate('注册', content, true);
}
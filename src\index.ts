import { getHomePage } from './pages/home';
import { getIndexPage } from './pages/index';
import { getLoginPage } from './pages/login';
import { getRegisterPage } from './pages/register';
import { AuthService } from './services/auth';
import { UserRegisterRequest, UserLoginRequest } from './types/auth';

export default {
	async fetch(request, env, ctx): Promise<Response> {
		const url = new URL(request.url);
		const path = url.pathname;
		const method = request.method;

		// 初始化认证服务
		const authService = new AuthService(env.DB);

		// API 端点
		if (path.startsWith('/api/')) {
			if (path === '/api/register' && method === 'POST') {
				try {
					const { email, password } = await request.json() as UserRegisterRequest;
					
					if (!email || !password) {
						return new Response(JSON.stringify({ success: false, message: '邮箱和密码不能为空' }), {
							status: 400,
							headers: { 'Content-Type': 'application/json' }
						});
					}

					// 简单的邮箱格式验证
					const emailRegex = /^[^\s@]+@[^\s@]+\.[^\s@]+$/;
					if (!emailRegex.test(email)) {
						return new Response(JSON.stringify({ success: false, message: '邮箱格式不正确' }), {
							status: 400,
							headers: { 'Content-Type': 'application/json' }
						});
					}

					// 密码长度验证
					if (password.length < 6) {
						return new Response(JSON.stringify({ success: false, message: '密码长度至少6位' }), {
							status: 400,
							headers: { 'Content-Type': 'application/json' }
						});
					}

					const result = await authService.register(email, password);
					return new Response(JSON.stringify(result), {
						status: result.success ? 200 : 400,
						headers: { 'Content-Type': 'application/json' }
					});
				} catch (error) {
					return new Response(JSON.stringify({ success: false, message: '请求数据格式错误' }), {
						status: 400,
						headers: { 'Content-Type': 'application/json' }
					});
				}
			}

			if (path === '/api/login' && method === 'POST') {
				try {
					const { email, password } = await request.json() as UserLoginRequest;
					
					if (!email || !password) {
						return new Response(JSON.stringify({ success: false, message: '邮箱和密码不能为空' }), {
							status: 400,
							headers: { 'Content-Type': 'application/json' }
						});
					}

					const result = await authService.login(email, password);
					
					if (result.success && result.sessionId) {
						// 设置会话Cookie
						const response = new Response(JSON.stringify({
							success: true,
							message: result.message,
							user: { id: result.user?.id, email: result.user?.email }
						}), {
							status: 200,
							headers: { 'Content-Type': 'application/json' }
						});
						
						// 设置HttpOnly Cookie 用于会话管理
						response.headers.set('Set-Cookie', `session_id=${result.sessionId}; HttpOnly; Secure; SameSite=Strict; Max-Age=604800; Path=/`);
						return response;
					}

					return new Response(JSON.stringify(result), {
						status: 401,
						headers: { 'Content-Type': 'application/json' }
					});
				} catch (error) {
					return new Response(JSON.stringify({ success: false, message: '请求数据格式错误' }), {
						status: 400,
						headers: { 'Content-Type': 'application/json' }
					});
				}
			}

			if (path === '/api/logout' && method === 'POST') {
				const cookies = request.headers.get('Cookie') || '';
				const sessionId = cookies.split(';').find(c => c.trim().startsWith('session_id='))?.split('=')[1];
				
				if (sessionId) {
					await authService.logout(sessionId);
				}

				const response = new Response(JSON.stringify({ success: true, message: '登出成功' }), {
					status: 200,
					headers: { 'Content-Type': 'application/json' }
				});
				
				// 清除会话Cookie
				response.headers.set('Set-Cookie', 'session_id=; HttpOnly; Secure; SameSite=Strict; Max-Age=0; Path=/');
				return response;
			}

			return new Response('API endpoint not found', { status: 404 });
		}

		switch (path) {
			case '/':
				// 检查用户登录状态
				const indexCookies = request.headers.get('Cookie') || '';
				const indexSessionId = indexCookies.split(';').find(c => c.trim().startsWith('session_id='))?.split('=')[1];
				
				let currentUser = null;
				if (indexSessionId) {
					const sessionValidation = await authService.validateSession(indexSessionId);
					if (sessionValidation.valid && sessionValidation.user) {
						currentUser = sessionValidation.user;
					}
				}
				
				return new Response(getIndexPage(currentUser), {
					headers: { 'Content-Type': 'text/html' }
				});
			case '/home':
				// 验证用户会话
				const cookies = request.headers.get('Cookie') || '';
				const sessionId = cookies.split(';').find(c => c.trim().startsWith('session_id='))?.split('=')[1];
				
				if (sessionId) {
					const sessionValidation = await authService.validateSession(sessionId);
					if (sessionValidation.valid && sessionValidation.user) {
						return new Response(getHomePage(sessionValidation.user), {
							headers: { 'Content-Type': 'text/html' }
						});
					}
				}
				
				// 会话无效，重定向到登录页
				return new Response('', {
					status: 302,
					headers: { 'Location': '/login' }
				});
			case '/login':
				return new Response(getLoginPage(), {
					headers: { 'Content-Type': 'text/html' }
				});
			case '/register':
				return new Response(getRegisterPage(), {
					headers: { 'Content-Type': 'text/html' }
				});
			case '/src/utils/icons.js':
				// 服务JavaScript文件
				const iconsJs = `
// 图标管理工具 - 统一管理所有Lucide图标的导入和初始化
export class IconManager {
  static icons = {};
  static createIcons = null;
  
  // 初始化图标库
  static async initialize() {
    try {
      const lucide = await import('https://esm.sh/lucide@latest');
      this.createIcons = lucide.createIcons;
      
      // 导入所有需要的图标
      this.icons = {
        Home: lucide.Home,
        User: lucide.User,
        LogOut: lucide.LogOut,
        Mail: lucide.Mail,
        Lock: lucide.Lock,
        Eye: lucide.Eye,
        EyeOff: lucide.EyeOff,
        Check: lucide.Check,
        X: lucide.X,
        AlertCircle: lucide.AlertCircle,
        Info: lucide.Info
      };
      
      return true;
    } catch (error) {
      console.error('Failed to load Lucide icons:', error);
      return false;
    }
  }
  
  // 创建图标实例
  static create(iconNames = []) {
    if (!this.createIcons) {
      console.warn('IconManager not initialized');
      return;
    }
    
    const iconsToCreate = {};
    
    // 如果没有指定图标，则创建所有已导入的图标
    if (iconNames.length === 0) {
      Object.assign(iconsToCreate, this.icons);
    } else {
      // 只创建指定的图标
      iconNames.forEach(name => {
        if (this.icons[name]) {
          iconsToCreate[name] = this.icons[name];
        } else {
          console.warn(\`Icon '\${name}' not found in loaded icons\`);
        }
      });
    }
    
    this.createIcons({ icons: iconsToCreate });
  }
  
  // 获取可用的图标列表
  static getAvailableIcons() {
    return Object.keys(this.icons);
  }
  
  // 检查图标是否可用
  static hasIcon(iconName) {
    return iconName in this.icons;
  }
}
				`;
				return new Response(iconsJs, {
					headers: { 'Content-Type': 'application/javascript' }
				});
			case '/styles.css':
				// 读取本地CSS文件内容
				const cssContent = `
/* 输入框基础样式 - 预设2px边框防止拖动 */
.input-container input {
  border: 2px solid transparent;
  transition: all 0.2s ease;
}

.input-container input:focus {
  border-color: #3b82f6;
  outline: none;
}

.input-active {
  border-color: #3b82f6 !important;
  background-color: white;
}

.input-container {
  position: relative;
}

.input-label {
  position: absolute;
  left: 16px;
  top: -8px;
  background: white;
  padding: 0 4px;
  font-size: 14px;
  color: #3b82f6;
  transition: all 0.2s ease;
  opacity: 0;
  transform: translateY(8px);
  pointer-events: none;
  z-index: 1;
}

.input-label.active {
  opacity: 1;
  transform: translateY(0);
}

.form-container {
  max-width: 500px;
  width: 90%;
  margin: 0 auto;
  padding: 3rem;
  background: white;
  border-radius: 1.5rem;
  box-shadow: 0 8px 25px -8px rgba(0, 0, 0, 0.15);
  /* 添加平滑的尺寸过渡动画 */
  transition: all 0.6s cubic-bezier(0.4, 0, 0.2, 1);
  overflow: hidden;
  /* 确保容器有最小高度以减少抖动 */
  min-height: 400px;
}

.slide-in {
  animation: slideIn 0.5s cubic-bezier(0.4, 0, 0.2, 1);
}

@keyframes slideIn {
  from { opacity: 0; transform: translateY(20px); }
  to { opacity: 1; transform: translateY(0); }
}

/* Additional animations for better UX */
.fade-in {
  animation: fadeIn 0.6s cubic-bezier(0.4, 0, 0.2, 1);
}

@keyframes fadeIn {
  from { opacity: 0; }
  to { opacity: 1; }
}

.scale-in {
  animation: scaleIn 0.5s cubic-bezier(0.4, 0, 0.2, 1);
}

@keyframes scaleIn {
  from { opacity: 0; transform: scale(0.95); }
  to { opacity: 1; transform: scale(1); }
}

/* 表单切换动画优化 */
.form-section {
  transition: all 0.5s cubic-bezier(0.4, 0, 0.2, 1);
}

.form-section.hidden {
  opacity: 0;
  transform: translateY(20px);
  pointer-events: none;
}

/* 防止输入框聚焦时布局拖动 */
.input-container input {
  box-sizing: border-box;
}

/* 确保所有输入框都有统一的边框 */
input[type="email"], input[type="password"] {
  border: 2px solid #d1d5db !important;
}

input[type="email"]:focus, input[type="password"]:focus {
  border-color: #3b82f6 !important;
}

/* 消息提示样式 */
.message-toast {
  position: fixed;
  top: 20px;
  right: 20px;
  z-index: 9999;
  min-width: 300px;
  max-width: 500px;
  opacity: 0;
  transform: translateX(100%);
  transition: all 0.3s cubic-bezier(0.68, -0.55, 0.265, 1.55);
}

.message-toast.message-show {
  opacity: 1;
  transform: translateX(0);
}

.message-toast.message-hide {
  opacity: 0;
  transform: translateX(100%);
}

.message-content {
  display: flex;
  align-items: center;
  padding: 12px 16px;
  border-radius: 8px;
  box-shadow: 0 4px 12px rgba(0, 0, 0, 0.15);
  background: white;
  border-left: 4px solid;
}

.message-success .message-content {
  border-left-color: #10b981;
  background-color: #f0fdf4;
}

.message-error .message-content {
  border-left-color: #ef4444;
  background-color: #fef2f2;
}

.message-warning .message-content {
  border-left-color: #f59e0b;
  background-color: #fffbeb;
}

.message-info .message-content {
  border-left-color: #3b82f6;
  background-color: #eff6ff;
}

.message-icon {
  margin-right: 12px;
  font-size: 16px;
  font-weight: bold;
}

.message-success .message-icon {
  color: #10b981;
}

.message-error .message-icon {
  color: #ef4444;
}

.message-warning .message-icon {
  color: #f59e0b;
}

.message-info .message-icon {
  color: #3b82f6;
}

.message-text {
  flex: 1;
  font-size: 14px;
  line-height: 1.4;
  color: #374151;
}

.message-close {
  margin-left: 12px;
  background: none;
  border: none;
  font-size: 18px;
  font-weight: bold;
  color: #9ca3af;
  cursor: pointer;
  padding: 0;
  width: 20px;
  height: 20px;
  display: flex;
  align-items: center;
  justify-content: center;
  border-radius: 50%;
  transition: all 0.2s ease;
}

.message-close:hover {
  background-color: rgba(0, 0, 0, 0.1);
  color: #374151;
}

/* 加载动画 */
.loading-spinner {
  display: inline-block;
  width: 14px;
  height: 14px;
  border: 2px solid #f3f3f3;
  border-top: 2px solid #3b82f6;
  border-radius: 50%;
  animation: spin 1s linear infinite;
  margin-right: 8px;
}

@keyframes spin {
  0% { transform: rotate(0deg); }
  100% { transform: rotate(360deg); }
}

/* 响应式设计 */
@media (max-width: 640px) {
  .message-toast {
    right: 10px;
    left: 10px;
    min-width: auto;
    max-width: none;
  }
}

/* 确认对话框样式 */
.confirm-overlay {
  position: fixed;
  top: 0;
  left: 0;
  right: 0;
  bottom: 0;
  background-color: rgba(0, 0, 0, 0.5);
  z-index: 10000;
  display: flex;
  align-items: center;
  justify-content: center;
  opacity: 0;
  transition: opacity 0.3s ease;
}

.confirm-overlay.show {
  opacity: 1;
}

.confirm-dialog {
  background: white;
  border-radius: 12px;
  padding: 24px;
  min-width: 320px;
  max-width: 90vw;
  box-shadow: 0 20px 25px -5px rgba(0, 0, 0, 0.1);
  transform: scale(0.95);
  transition: transform 0.3s ease;
}

.confirm-overlay.show .confirm-dialog {
  transform: scale(1);
}

.confirm-title {
  font-size: 18px;
  font-weight: 600;
  color: #374151;
  margin-bottom: 12px;
}

.confirm-message {
  color: #6b7280;
  margin-bottom: 20px;
  line-height: 1.5;
}

.confirm-buttons {
  display: flex;
  gap: 12px;
  justify-content: flex-end;
}

.confirm-button {
  padding: 8px 16px;
  border-radius: 6px;
  font-size: 14px;
  font-weight: 500;
  cursor: pointer;
  border: none;
  transition: all 0.2s ease;
}

.confirm-button.cancel {
  background-color: #f3f4f6;
  color: #374151;
}

.confirm-button.cancel:hover {
  background-color: #e5e7eb;
}

.confirm-button.confirm {
  background-color: #ef4444;
  color: white;
}

.confirm-button.confirm:hover {
  background-color: #dc2626;
}
				`;
				return new Response(cssContent, {
					headers: { 'Content-Type': 'text/css' }
				});
			default:
				return new Response('404 Not Found', { status: 404 });
		}
	},
} satisfies ExportedHandler<Env>;

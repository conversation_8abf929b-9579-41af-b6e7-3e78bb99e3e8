import React, { useState, useEffect } from 'react';
import { motion, AnimatePresence } from 'framer-motion';

export const LoginPage: React.FC = () => {
  const [currentView, setCurrentView] = useState<'loginInitial' | 'loginPassword' | 'registerInitial' | 'registerPassword'>('loginInitial');
  const [loginEmail, setLoginEmail] = useState('');
  const [registerEmail, setRegisterEmail] = useState('');
  const [showPassword, setShowPassword] = useState(false);

  useEffect(() => {
    if (window.location.hash === '#register') {
      setCurrentView('registerInitial');
    } else {
      setCurrentView('loginInitial');
    }
  }, []);

  const slideAnimation = {
    initial: { opacity: 0, y: 20 },
    animate: { opacity: 1, y: 0 },
    exit: { opacity: 0, y: -20 },
    transition: {
      duration: 0.5,
      ease: [0.4, 0, 0.2, 1] // cubic-bezier缓动函数
    }
  };

  const handleLoginEmail = (e: React.FormEvent, email: string) => {
    e.preventDefault();
    setLoginEmail(email);
    setCurrentView('loginPassword');
  };

  const handleRegisterEmail = (e: React.FormEvent, email: string) => {
    e.preventDefault();
    setRegisterEmail(email);
    setCurrentView('registerPassword');
  };

  const switchToRegister = () => {
    setCurrentView('registerInitial');
    window.location.hash = '#register';
  };

  const switchToLogin = () => {
    setCurrentView('loginInitial');
    window.location.hash = '#login';
  };

  const editLoginEmail = () => setCurrentView('loginInitial');
  const editRegisterEmail = () => setCurrentView('registerInitial');

  const togglePasswordVisibility = () => setShowPassword(!showPassword);

  const handleLoginSubmit = (e: React.FormEvent) => {
    e.preventDefault();
    alert('登录功能正在开发中...');
  };

  const handleRegisterSubmit = (e: React.FormEvent) => {
    e.preventDefault();
    alert('注册功能正在开发中...');
  };

  const forgotPassword = () => {
    alert('重置密码功能正在开发中...');
  };

  return (
    <div className="bg-gray-50 min-h-screen flex items-center justify-center p-4">
      <motion.div
        className="form-container max-w-xs w-full mx-auto p-6 bg-white rounded-2xl shadow-md"
        initial={{ opacity: 0, scale: 0.95 }}
        animate={{ opacity: 1, scale: 1 }}
        transition={{
          duration: 0.6,
          ease: [0.4, 0, 0.2, 1]
        }}
        layout // 启用布局动画
        layoutRoot // 设置为布局根节点
      >
        <AnimatePresence mode="wait" initial={false}>
          {currentView === 'loginInitial' && (
            <motion.div
              key="loginInitial"
              className="text-center flex flex-col items-center"
              {...slideAnimation}
              layout // 启用布局动画
            >
              <h2 className="text-lg font-semibold text-gray-800 mb-6">欢迎回来</h2>
              <form onSubmit={(e) => {
                e.preventDefault();
                const email = (e.target as any).loginEmail.value;
                handleLoginEmail(e, email);
              }} className="space-y-4">
                <div className="relative">
                  <motion.input
                    name="loginEmail"
                    type="email"
                    placeholder="电子邮件地址"
                    className="w-full px-4 py-3 border border-gray-300 rounded-full text-center text-gray-600 focus:outline-none focus:border-blue-500 transition-all duration-200"
                    required
                    whileFocus={{ scale: 1.02 }}
                  />
                </div>
                <motion.button 
                  type="submit" 
                  className="w-full bg-gray-900 text-white py-3 rounded-full font-medium hover:bg-gray-800 transition-colors"
                  whileHover={{ scale: 1.02 }}
                  whileTap={{ scale: 0.98 }}
                >
                  继续
                </motion.button>
              </form>
              <p className="mt-4 text-gray-600">
                还没有帐户？ 
                <button onClick={switchToRegister} className="text-blue-600 hover:underline ml-1">请注册</button>
              </p>
            </motion.div>
          )}

          {currentView === 'loginPassword' && (
            <motion.div
              key="loginPassword"
              className="text-center flex flex-col items-center"
              {...slideAnimation}
              layout // 启用布局动画
            >
              <h2 className="text-lg font-semibold text-gray-800 mb-6">输入密码</h2>
              <form onSubmit={handleLoginSubmit} className="space-y-4">
                <div className="relative">
                  <div className="flex items-center justify-between text-sm text-gray-500 mb-2">
                    <span>电子邮件地址</span>
                  </div>
                  <motion.div 
                    className="w-full flex items-center border border-gray-300 rounded-full px-4 py-3 bg-gray-50"
                    whileHover={{ scale: 1.01 }}
                  >
                    <span className="flex-1 text-gray-700">{loginEmail}</span>
                    <button type="button" onClick={editLoginEmail} className="text-blue-600 hover:underline text-sm font-medium">编辑</button>
                  </motion.div>
                </div>
                <div>
                  <div className="flex items-center justify-between text-sm text-gray-500 mb-2">
                    <span>密码</span>
                  </div>
                  <div className="relative">
                    <motion.input
                      type={showPassword ? 'text' : 'password'}
                      className="w-full px-4 py-3 border border-gray-300 rounded-full text-gray-600 focus:outline-none focus:border-blue-500 transition-all duration-200"
                      required
                      whileFocus={{ scale: 1.02 }}
                    />
                    <button type="button" onClick={togglePasswordVisibility} className="absolute right-4 top-1/2 transform -translate-y-1/2 text-gray-400 hover:text-gray-600">
                      <svg width="20" height="20" fill="currentColor" viewBox="0 0 24 24">
                        <path d="M12 4.5C7 4.5 2.73 7.61 1 12c1.73 4.39 6 7.5 11 7.5s9.27-3.11 11-7.5c-1.73-4.39-6-7.5-11-7.5zM12 17c-2.76 0-5-2.24-5-5s2.24-5 5-5 5 2.24 5 5-2.24 5-5 5zm0-8c-1.66 0-3 1.34-3 3s1.34 3 3 3 3-1.34 3-3-1.34-3-3-3z"/>
                      </svg>
                    </button>
                  </div>
                </div>
                <div className="text-left">
                  <button type="button" onClick={forgotPassword} className="text-blue-600 hover:underline text-sm">忘记了密码？</button>
                </div>
                <motion.button 
                  type="submit" 
                  className="w-full bg-gray-900 text-white py-3 rounded-full font-medium hover:bg-gray-800 transition-colors"
                  whileHover={{ scale: 1.02 }}
                  whileTap={{ scale: 0.98 }}
                >
                  继续
                </motion.button>
              </form>
              <p className="mt-4 text-gray-600">
                还没有帐户？ 
                <button onClick={switchToRegister} className="text-blue-600 hover:underline ml-1">请注册</button>
              </p>
            </motion.div>
          )}

          {currentView === 'registerInitial' && (
            <motion.div
              key="registerInitial"
              className="text-center flex flex-col items-center"
              {...slideAnimation}
              layout // 启用布局动画
            >
              <h2 className="text-lg font-semibold text-gray-800 mb-6">创建账户</h2>
              <form onSubmit={(e) => {
                e.preventDefault();
                const email = (e.target as any).registerEmail.value;
                handleRegisterEmail(e, email);
              }} className="space-y-4">
                <div className="relative">
                  <motion.input
                    name="registerEmail"
                    type="email"
                    placeholder="电子邮件地址"
                    className="w-full px-4 py-3 border border-gray-300 rounded-full text-center text-gray-600 focus:outline-none focus:border-blue-500 transition-all duration-200"
                    required
                    whileFocus={{ scale: 1.02 }}
                  />
                </div>
                <motion.button 
                  type="submit" 
                  className="w-full bg-gray-900 text-white py-3 rounded-full font-medium hover:bg-gray-800 transition-colors"
                  whileHover={{ scale: 1.02 }}
                  whileTap={{ scale: 0.98 }}
                >
                  继续
                </motion.button>
              </form>
              <p className="mt-4 text-gray-600">
                已有帐户？ 
                <button onClick={switchToLogin} className="text-blue-600 hover:underline ml-1">请登录</button>
              </p>
            </motion.div>
          )}

          {currentView === 'registerPassword' && (
            <motion.div
              key="registerPassword"
              className="text-center flex flex-col items-center"
              {...slideAnimation}
              layout // 启用布局动画
            >
              <h2 className="text-lg font-semibold text-gray-800 mb-6">创建账户</h2>
              <form onSubmit={handleRegisterSubmit} className="space-y-4">
                <div className="relative">
                  <div className="flex items-center justify-between text-sm text-gray-500 mb-2">
                    <span>电子邮件地址</span>
                  </div>
                  <motion.div 
                    className="w-full flex items-center border border-gray-300 rounded-full px-4 py-3 bg-gray-50"
                    whileHover={{ scale: 1.01 }}
                  >
                    <span className="flex-1 text-gray-700">{registerEmail}</span>
                    <button type="button" onClick={editRegisterEmail} className="text-blue-600 hover:underline text-sm font-medium">编辑</button>
                  </motion.div>
                </div>
                <div>
                  <div className="flex items-center justify-between text-sm text-gray-500 mb-2">
                    <span>密码</span>
                  </div>
                  <div className="relative">
                    <motion.input
                      type={showPassword ? 'text' : 'password'}
                      className="w-full px-4 py-3 border border-gray-300 rounded-full text-gray-600 focus:outline-none focus:border-blue-500 transition-all duration-200"
                      required
                      whileFocus={{ scale: 1.02 }}
                    />
                    <button type="button" onClick={togglePasswordVisibility} className="absolute right-4 top-1/2 transform -translate-y-1/2 text-gray-400 hover:text-gray-600">
                      <svg width="20" height="20" fill="currentColor" viewBox="0 0 24 24">
                        <path d="M12 4.5C7 4.5 2.73 7.61 1 12c1.73 4.39 6 7.5 11 7.5s9.27-3.11 11-7.5c-1.73-4.39-6-7.5-11-7.5zM12 17c-2.76 0-5-2.24-5-5s2.24-5 5-5 5 2.24 5 5-2.24 5-5 5zm0-8c-1.66 0-3 1.34-3 3s1.34 3 3 3 3-1.34 3-3-1.34-3-3-3z"/>
                      </svg>
                    </button>
                  </div>
                </div>
                <motion.button 
                  type="submit" 
                  className="w-full bg-gray-900 text-white py-3 rounded-full font-medium hover:bg-gray-800 transition-colors"
                  whileHover={{ scale: 1.02 }}
                  whileTap={{ scale: 0.98 }}
                >
                  继续
                </motion.button>
              </form>
              <p className="mt-4 text-gray-600">
                已有帐户？ 
                <button onClick={switchToLogin} className="text-blue-600 hover:underline ml-1">请登录</button>
              </p>
            </motion.div>
          )}
        </AnimatePresence>
      </motion.div>
    </div>
  );
};
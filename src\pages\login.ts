import { renderHTMLTemplate } from '../utils/template';

export function getLoginPage(): string {
  const content = `
    <div class="bg-gray-50 min-h-screen flex items-center justify-center p-4">
      <div id="form-container" class="form-container">
        
        <!-- 初始登录页面 -->
        <div id="loginInitial" class="text-center">
          <h2 class="text-2xl font-semibold text-gray-800 mb-10">欢迎回来</h2>
          <form class="space-y-6">
            <div class="input-container">
              <input 
                id="loginEmail"
                type="email" 
                placeholder="电子邮件地址" 
                class="w-full px-4 py-4 rounded-full text-center text-gray-600 focus:outline-none transition-all duration-200"
                required
              >
              <label class="input-label">电子邮件地址</label>
            </div>
            <button type="button" id="loginEmailBtn" class="w-full bg-gray-900 text-white py-4 rounded-full font-medium hover:bg-gray-800 transition-colors">
              继续
            </button>
          </form>
          <p class="mt-8 text-gray-600">
            还没有帐户？ 
            <a href="/register" class="text-blue-600 hover:underline">请注册</a>
          </p>
        </div>

        <!-- 登录密码输入页面 -->
        <div id="loginPassword" class="text-center hidden">
          <h2 class="text-2xl font-semibold text-gray-800 mb-10">输入密码</h2>
          <form class="space-y-6">
            <div class="relative">
              <div class="flex items-center justify-between text-sm text-gray-500 mb-2">
                <span>电子邮件地址</span>
              </div>
              <div class="flex items-center border border-gray-300 rounded-full px-4 py-3 bg-gray-50">
                <span id="displayLoginEmail" class="flex-1 text-gray-700"></span>
                <button type="button" id="editLoginEmailBtn" class="text-blue-600 hover:underline text-sm font-medium">编辑</button>
              </div>
            </div>
            <div>
              <div class="flex items-center justify-between text-sm text-gray-500 mb-2">
                <span>密码</span>
              </div>
              <div class="relative input-container">
                <input 
                  id="loginPasswordInput"
                  type="password" 
                  class="w-full px-4 py-4 rounded-full text-gray-600 focus:outline-none transition-all duration-200"
                  required
                >
                <label class="input-label">密码</label>
              </div>
            </div>
            <div class="text-left">
              <button type="button" id="forgotPasswordBtn" class="text-blue-600 hover:underline text-sm">忘记了密码？</button>
            </div>
            <button type="button" id="loginSubmitBtn" class="w-full bg-gray-900 text-white py-4 rounded-full font-medium hover:bg-gray-800 transition-colors">
              继续
            </button>
          </form>
          <p class="mt-8 text-gray-600">
            还没有帐户？ 
            <a href="/register" class="text-blue-600 hover:underline">请注册</a>
          </p>
        </div>
      </div>
    </div>

    <script>
      console.log('Login page script loading...');
      
      // 全局变量
      let motion = null;
      
      // 消息提示函数
      function showMessage(message, type = 'info', duration = 3000) {
        const existingMessage = document.getElementById('message-toast');
        if (existingMessage) {
          existingMessage.remove();
        }

        const messageDiv = document.createElement('div');
        messageDiv.id = 'message-toast';
        messageDiv.className = \`message-toast message-\${type}\`;
        
        const icons = {
          success: '✓',
          error: '✕',
          warning: '⚠',
          info: 'ℹ'
        };
        
        messageDiv.innerHTML = \`
          <div class="message-content">
            <span class="message-icon">\${icons[type] || icons.info}</span>
            <span class="message-text">\${message}</span>
            <button class="message-close" onclick="this.parentElement.parentElement.remove()">×</button>
          </div>
        \`;

        document.body.appendChild(messageDiv);

        setTimeout(() => {
          messageDiv.classList.add('message-show');
        }, 10);

        if (duration > 0) {
          setTimeout(() => {
            messageDiv.classList.add('message-hide');
            setTimeout(() => {
              if (messageDiv.parentElement) {
                messageDiv.remove();
              }
            }, 300);
          }, duration);
        }

        return messageDiv;
      }
      
      // 基本函数
      function hideAllForms() {
        const forms = ['loginInitial', 'loginPassword'];
        forms.forEach(id => {
          const element = document.getElementById(id);
          if (element) {
            element.classList.add('hidden');
          }
        });
      }
      
      function showForm(formId) {
        const form = document.getElementById(formId);
        if (form) {
          form.classList.remove('hidden');
          
          // 如果有motion，添加动画
          if (motion) {
            motion(form, {
              initial: { opacity: 0, y: 20 },
              animate: { opacity: 1, y: 0 },
              transition: { duration: 0.3 }
            });
          }
        }
      }
      
      function handleLoginEmail() {
        console.log('Handling login email');
        const email = document.getElementById('loginEmail').value;
        if (!email) {
          showMessage('请输入邮箱地址', 'warning');
          return;
        }

        // 简单的邮箱格式验证
        const emailRegex = /^[^\\s@]+@[^\\s@]+\\.[^\\s@]+$/;
        if (!emailRegex.test(email)) {
          showMessage('邮箱格式不正确', 'error');
          return;
        }

        document.getElementById('displayLoginEmail').textContent = email;
        hideAllForms();
        setTimeout(() => showForm('loginPassword'), 100);
      }
      
      function editLoginEmail() {
        hideAllForms();
        setTimeout(() => showForm('loginInitial'), 100);
      }
      
      function handleLoginSubmit() {
        console.log('Handling login submit');
        const email = document.getElementById('displayLoginEmail').textContent;
        const password = document.getElementById('loginPasswordInput').value;
        
        if (!password) {
          showMessage('请输入密码', 'warning');
          return;
        }

        // 显示加载状态
        const submitBtn = document.getElementById('loginSubmitBtn');
        const originalText = submitBtn.textContent;
        submitBtn.textContent = '登录中...';
        submitBtn.disabled = true;

        const loadingMessage = showMessage('<span class="loading-spinner"></span> 正在登录...', 'info', 0);

        // 调用登录API
        fetch('/api/login', {
          method: 'POST',
          headers: {
            'Content-Type': 'application/json',
          },
          body: JSON.stringify({ email, password })
        })
        .then(response => response.json())
        .then(data => {
          // 移除加载消息
          if (loadingMessage && loadingMessage.parentElement) {
            loadingMessage.remove();
          }

          if (data.success) {
            showMessage('登录成功！即将跳转到首页', 'success', 1500);
            setTimeout(() => {
              window.location.href = '/home';
            }, 1500);
          } else {
            showMessage(data.message || '登录失败，请检查邮箱和密码', 'error');
          }
        })
        .catch(error => {
          console.error('登录错误:', error);
          // 移除加载消息
          if (loadingMessage && loadingMessage.parentElement) {
            loadingMessage.remove();
          }
          showMessage('网络错误，请稍后重试', 'error');
        })
        .finally(() => {
          // 恢复按钮状态
          submitBtn.textContent = originalText;
          submitBtn.disabled = false;
        });
      }
      
      function forgotPassword() {
        showMessage('重置密码功能正在开发中...', 'info');
      }
      
      // DOM加载完成后初始化
      document.addEventListener('DOMContentLoaded', function() {
        console.log('DOM loaded, binding events...');
        
        // 绑定所有按钮事件
        const loginEmailBtn = document.getElementById('loginEmailBtn');
        if (loginEmailBtn) {
          loginEmailBtn.addEventListener('click', function(e) {
            e.preventDefault();
            console.log('Login email button clicked');
            handleLoginEmail();
          });
        }
        
        const editLoginEmailBtn = document.getElementById('editLoginEmailBtn');
        if (editLoginEmailBtn) {
          editLoginEmailBtn.addEventListener('click', function(e) {
            e.preventDefault();
            editLoginEmail();
          });
        }
        
        const loginSubmitBtn = document.getElementById('loginSubmitBtn');
        if (loginSubmitBtn) {
          loginSubmitBtn.addEventListener('click', function(e) {
            e.preventDefault();
            handleLoginSubmit();
          });
        }
        
        const forgotPasswordBtn = document.getElementById('forgotPasswordBtn');
        if (forgotPasswordBtn) {
          forgotPasswordBtn.addEventListener('click', function(e) {
            e.preventDefault();
            forgotPassword();
          });
        }
        
        // 初始化显示登录表单
        showForm('loginInitial');
        
        console.log('All events bound successfully');
      });
    </script>
    
    <script type="module">
      import { motion as framerMotion } from 'https://esm.sh/framer-motion@11.0.24';
      
      // 设置全局motion变量
      motion = framerMotion;
      
      document.addEventListener('DOMContentLoaded', function() {
        console.log('Framer Motion loaded');
        
        // 初始化动画
        const formContainer = document.getElementById('form-container');
        if (formContainer) {
          framerMotion(formContainer, {
            initial: { opacity: 0, scale: 0.95 },
            animate: { opacity: 1, scale: 1 },
            transition: { duration: 0.5 }
          });
        }
      });
    </script>
  `;
  
  return renderHTMLTemplate('登录', content, true);
}
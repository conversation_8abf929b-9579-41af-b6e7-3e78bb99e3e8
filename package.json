{"name": "cloudflare-project-2", "version": "0.0.0", "private": true, "scripts": {"deploy": "wrangler deploy", "dev": "wrangler dev", "start": "wrangler dev", "test": "vitest", "cf-typegen": "wrangler types", "build:css": "tailwindcss -i ./src/styles.css -o ./dist/styles.css --watch", "build:css:prod": "tailwindcss -i ./src/styles.css -o ./dist/styles.css --minify"}, "devDependencies": {"@cloudflare/vitest-pool-workers": "^0.8.19", "@types/react": "^19.1.9", "@types/react-dom": "^19.1.7", "tailwindcss": "^4.1.11", "typescript": "^5.5.2", "vitest": "~3.2.0", "wrangler": "^4.26.1"}, "dependencies": {"framer-motion": "^12.23.11", "lucide": "^0.534.0", "react": "^19.1.1", "react-dom": "^19.1.1"}}